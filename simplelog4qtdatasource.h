#ifndef SIMPLELOG4QTDATASOURCE_H
#define SIMPLELOG4QTDATASOURCE_H

#include "idatasource.h"
#include <QTimer>
#include <QMutex>

// 条件前置声明
#ifdef LOG4QT_AVAILABLE
namespace Log4Qt {
    class Logger;
}
#endif

class SimpleLogViewerAppender;

/**
 * @brief 简化的Log4Qt数据源
 * 
 * 重构后的Log4Qt数据源实现，特点：
 * - 简化了Log4Qt连接逻辑
 * - 移除了复杂的轮询机制
 * - 保留了核心的日志接收功能
 * - 支持实时日志接收
 * - 简化的错误处理
 * - 线程安全的数据访问
 */
class LOGVIEWER_EXPORT SimpleLog4QtDataSource : public IDataSource
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SimpleLog4QtDataSource(QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SimpleLog4QtDataSource() override;

    // ========== IDataSource接口实现 ==========
    
    /**
     * @brief 连接到Log4Qt数据源
     * @return 连接是否成功
     */
    bool connectToSource() override;

    /**
     * @brief 同步加载Log4Qt数据
     * @return 加载的日志条目列表
     */
    QVector<LogEntry> loadData() override;

    /**
     * @brief 断开连接
     */
    void disconnect() override;

    /**
     * @brief 检查是否已连接
     * @return 连接状态
     */
    bool isConnected() const override;

    /**
     * @brief 获取数据源信息
     * @return 数据源描述字符串
     */
    QString getSourceInfo() const override;

    // ========== Log4Qt数据源特有方法 ==========
    
    /**
     * @brief 设置日志器名称
     * @param loggerName 日志器名称
     */
    void setLoggerName(const QString& loggerName);

    /**
     * @brief 获取日志器名称
     * @return 当前日志器名称
     */
    QString getLoggerName() const { return m_loggerName; }

    /**
     * @brief 设置最大缓存条目数
     * @param maxEntries 最大条目数
     */
    void setMaxCacheEntries(int maxEntries);

    /**
     * @brief 获取最大缓存条目数
     * @return 最大条目数
     */
    int getMaxCacheEntries() const { return m_maxCacheEntries; }

    /**
     * @brief 清除缓存的日志条目
     */
    void clearCache();

    /**
     * @brief 获取缓存的日志条目数量
     * @return 缓存条目数量
     */
    int getCachedEntryCount() const;

    /**
     * @brief 启用实时日志接收
     * @param enabled 是否启用
     */
    void setRealTimeEnabled(bool enabled);

    /**
     * @brief 检查是否启用了实时日志接收
     * @return 实时接收状态
     */
    bool isRealTimeEnabled() const { return m_realTimeEnabled; }

    /**
     * @brief 生成测试日志数据
     * @param count 生成的日志条目数量
     */
    void generateTestData(int count = 10);

private slots:
    /**
     * @brief 处理新的日志条目
     * @param entry 日志条目
     */
    void onNewLogEntry(const LogEntry& entry);

    /**
     * @brief 定时检查新日志
     */
    void checkForNewLogs();

private:
    // ========== Log4Qt处理方法 ==========
    
    /**
     * @brief 初始化Log4Qt环境
     * @return 初始化是否成功
     */
    bool initializeLog4Qt();

    /**
     * @brief 创建并配置Logger
     * @return Logger创建是否成功
     */
    bool createLogger();

    /**
     * @brief 创建并配置Appender
     * @return Appender创建是否成功
     */
    bool createAppender();

    /**
     * @brief 清理Log4Qt资源
     */
    void cleanupLog4Qt();

    /**
     * @brief 检查Log4Qt是否可用
     * @return Log4Qt可用性
     */
    bool isLog4QtAvailable() const;

    // ========== 缓存管理方法 ==========
    
    /**
     * @brief 添加日志条目到缓存
     * @param entry 日志条目
     */
    void addToCache(const LogEntry& entry);

    /**
     * @brief 清理过期的缓存条目
     */
    void cleanupCache();

    /**
     * @brief 检查内存使用情况
     */
    void checkMemoryUsage();

    // ========== 辅助方法 ==========
    
    /**
     * @brief 发出错误信号
     * @param error 错误描述
     */
    void emitError(const QString& error);

    /**
     * @brief 发出状态变化信号
     * @param status 状态描述
     */
    void emitStatusChanged(const QString& status);

private:
    // ========== 核心属性 ==========
    QString m_loggerName;                   ///< 日志器名称
    bool m_connected;                       ///< 连接状态
    bool m_realTimeEnabled;                 ///< 实时接收启用状态
    int m_maxCacheEntries;                  ///< 最大缓存条目数
    
    // ========== Log4Qt组件 ==========
#ifdef LOG4QT_AVAILABLE
    Log4Qt::Logger* m_logger;               ///< Log4Qt日志器
#else
    void* m_logger;                         ///< 简化版本的日志器占位符
#endif
    SimpleLogViewerAppender* m_appender;    ///< 简化的Appender
    
    // ========== 数据缓存 ==========
    QVector<LogEntry> m_cachedEntries;      ///< 缓存的日志条目
    mutable QMutex m_cacheMutex;            ///< 缓存访问互斥锁
    
    // ========== 定时器 ==========
    QTimer* m_checkTimer;                   ///< 检查新日志的定时器
    
    // ========== 统计信息 ==========
    int m_totalReceived;                    ///< 总接收条目数
    QDateTime m_lastReceiveTime;            ///< 最后接收时间
    
    // ========== 常量定义 ==========
    static const int DEFAULT_MAX_CACHE_ENTRIES;    ///< 默认最大缓存条目数
    static const int CHECK_INTERVAL_MS;             ///< 检查间隔（毫秒）
    static const int CACHE_CLEANUP_THRESHOLD;      ///< 缓存清理阈值
    static const int EMERGENCY_CLEANUP_THRESHOLD;  ///< 紧急清理阈值
    static const int CACHE_CHECK_INTERVAL;         ///< 缓存检查间隔
};

#endif // SIMPLELOG4QTDATASOURCE_H
