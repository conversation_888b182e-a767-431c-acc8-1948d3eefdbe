#include "simplelog4qtdatasource.h"
#include "simpleconfigmanager.h"
#include "simplelogviewerappender.h"
#include "logger.h"  // 添加用户的Logger类

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/logger.h>
#include <log4qt/logmanager.h>
#include <log4qt/basicconfigurator.h>
#endif

#include <QDebug>
#include <QDateTime>
#include <QMutexLocker>
#include <QApplication>
#include <QCoreApplication>

// ========== 常量定义 ==========
const int SimpleLog4QtDataSource::DEFAULT_MAX_CACHE_ENTRIES = 10000;
const int SimpleLog4QtDataSource::CHECK_INTERVAL_MS = 1000; // 1秒
const int SimpleLog4QtDataSource::CACHE_CLEANUP_THRESHOLD = 15000;
const int SimpleLog4QtDataSource::EMERGENCY_CLEANUP_THRESHOLD = 20000; // 紧急清理阈值
const int SimpleLog4QtDataSource::CACHE_CHECK_INTERVAL = 10; // 改为10秒检查一次

SimpleLog4QtDataSource::SimpleLog4QtDataSource(QObject* parent)
    : IDataSource(parent)
    , m_loggerName("TestLogger")
    , m_connected(false)
    , m_realTimeEnabled(true)
    , m_maxCacheEntries(DEFAULT_MAX_CACHE_ENTRIES)
    , m_logger(nullptr)
    , m_appender(nullptr)
    , m_checkTimer(nullptr)
    , m_totalReceived(0)
{
    // 从配置管理器获取设置
    SimpleConfigManager& config = SimpleConfigManager::instance();
    m_maxCacheEntries = config.getMaxLogEntries();
    
    // 创建定时器
    m_checkTimer = new QTimer(this);
    m_checkTimer->setInterval(CHECK_INTERVAL_MS);
    connect(m_checkTimer, &QTimer::timeout,
            this, &SimpleLog4QtDataSource::checkForNewLogs);
    
    qDebug() << "SimpleLog4QtDataSource created with max cache entries:" << m_maxCacheEntries;
}

SimpleLog4QtDataSource::~SimpleLog4QtDataSource()
{
    disconnect();
    qDebug() << "SimpleLog4QtDataSource destroyed";
}

bool SimpleLog4QtDataSource::connectToSource()
{
    if (m_loggerName.isEmpty()) {
        emitError("日志器名称为空");
        return false;
    }
    
    if (!isLog4QtAvailable()) {
        emitError("Log4Qt库不可用");
        return false;
    }
    
    // 初始化Log4Qt环境
    if (!initializeLog4Qt()) {
        emitError("Log4Qt初始化失败");
        return false;
    }
    
    // 创建Logger
    if (!createLogger()) {
        emitError("创建Logger失败");
        return false;
    }
    
    // 创建Appender
    if (!createAppender()) {
        emitError("创建Appender失败");
        return false;
    }
    
    m_connected = true;
    
    // 启动定时检查（如果启用实时接收）
    if (m_realTimeEnabled) {
        m_checkTimer->start();
        qDebug() << "Real-time log checking started";
    }
    
    emitStatusChanged(QString("已连接到Log4Qt日志器: %1").arg(m_loggerName));
    
    qDebug() << "Connected to Log4Qt logger:" << m_loggerName;
    return true;
}

QVector<LogEntry> SimpleLog4QtDataSource::loadData()
{
    QMutexLocker locker(&m_cacheMutex);
    
    if (!m_connected) {
        emitError("未连接到数据源");
        return QVector<LogEntry>();
    }
    
    // 返回缓存的数据副本
    QVector<LogEntry> result = m_cachedEntries;
    
    emitStatusChanged(QString("已加载 %1 条Log4Qt日志").arg(result.size()));
    
    qDebug() << "Loaded" << result.size() << "entries from Log4Qt cache";
    return result;
}

void SimpleLog4QtDataSource::disconnect()
{
    // 停止定时器
    if (m_checkTimer) {
        m_checkTimer->stop();
    }
    
    // 清理Log4Qt资源
    cleanupLog4Qt();
    
    m_connected = false;
    emitStatusChanged("已断开Log4Qt连接");
    
    qDebug() << "Disconnected from Log4Qt data source";
}

bool SimpleLog4QtDataSource::isConnected() const
{
    return m_connected;
}

QString SimpleLog4QtDataSource::getSourceInfo() const
{
    QString info = QString("Log4Qt日志器: %1\n").arg(m_loggerName);
    info += QString("连接状态: %1\n").arg(m_connected ? "已连接" : "未连接");
    info += QString("实时接收: %1\n").arg(m_realTimeEnabled ? "启用" : "禁用");
    info += QString("缓存条目: %1/%2\n").arg(getCachedEntryCount()).arg(m_maxCacheEntries);
    info += QString("总接收数: %1\n").arg(m_totalReceived);
    
    if (m_lastReceiveTime.isValid()) {
        info += QString("最后接收: %1").arg(m_lastReceiveTime.toString("yyyy-MM-dd hh:mm:ss"));
    } else {
        info += "最后接收: 无";
    }
    
    return info;
}

void SimpleLog4QtDataSource::setLoggerName(const QString& loggerName)
{
    if (m_loggerName != loggerName) {
        // 如果已连接，先断开
        if (m_connected) {
            disconnect();
        }
        
        m_loggerName = loggerName;
        qDebug() << "Logger name set to:" << loggerName;
    }
}

void SimpleLog4QtDataSource::setMaxCacheEntries(int maxEntries)
{
    if (maxEntries > 0 && maxEntries != m_maxCacheEntries) {
        m_maxCacheEntries = maxEntries;
        
        // 如果当前缓存超过新限制，进行清理
        QMutexLocker locker(&m_cacheMutex);
        if (m_cachedEntries.size() > maxEntries) {
            int removeCount = m_cachedEntries.size() - maxEntries;
            m_cachedEntries.remove(0, removeCount);
            qDebug() << "Cache trimmed, removed" << removeCount << "entries";
        }
        
        qDebug() << "Max cache entries set to:" << maxEntries;
    }
}

void SimpleLog4QtDataSource::clearCache()
{
    QMutexLocker locker(&m_cacheMutex);
    int oldSize = m_cachedEntries.size();
    m_cachedEntries.clear();
    
    emitStatusChanged("缓存已清除");
    qDebug() << "Cache cleared, removed" << oldSize << "entries";
}

int SimpleLog4QtDataSource::getCachedEntryCount() const
{
    QMutexLocker locker(&m_cacheMutex);
    return m_cachedEntries.size();
}

void SimpleLog4QtDataSource::setRealTimeEnabled(bool enabled)
{
    if (m_realTimeEnabled != enabled) {
        m_realTimeEnabled = enabled;
        
        if (m_connected && m_checkTimer) {
            if (enabled) {
                m_checkTimer->start();
                qDebug() << "Real-time log checking enabled";
            } else {
                m_checkTimer->stop();
                qDebug() << "Real-time log checking disabled";
            }
        }
    }
}

void SimpleLog4QtDataSource::generateTestData(int count)
{
    if (!m_connected) {
        emitError("未连接到数据源，无法生成测试数据");
        return;
    }

    if (!m_appender) {
        emitError("Appender未初始化，无法生成测试数据");
        return;
    }

    emitStatusChanged(QString("正在生成 %1 条测试数据...").arg(count));

#ifdef LOG4QT_AVAILABLE
    // 使用用户的Logger类生成测试日志
    Logger userLogger = Logger::getInstance();

    for (int i = 0; i < count; ++i) {
        QString testMessage = QString("测试日志消息 #%1 - 来自LogViewer").arg(i + 1);

        // 使用不同的日志级别
        switch (i % 5) {
        case 0:
            userLogger.log(Debug, "root", testMessage);
            break;
        case 1:
            userLogger.log(Info, "root", testMessage);
            break;
        case 2:
            userLogger.log(Warn, "root", testMessage);
            break;
        case 3:
            userLogger.log(Error, "root", testMessage);
            break;
        case 4:
            userLogger.log(Fatal, "root", testMessage);
            break;
        }

        // 也生成一些通信日志
        if (i % 3 == 0) {
            QString commMessage = QString("通信日志消息 #%1").arg(i + 1);
            userLogger.log(Info, "commLogger", commMessage);
        }
    }

    qDebug() << "Generated" << count << "test log entries using custom Logger format";
#else
    // 使用SimpleLogViewerAppender生成测试数据
    m_appender->generateTestEntries(count);
    qDebug() << "Generated" << count << "test log entries using SimpleLogViewerAppender";
#endif

    emitStatusChanged(QString("测试数据生成完成，共 %1 条").arg(count));
}

void SimpleLog4QtDataSource::onNewLogEntry(const LogEntry& entry)
{
    addToCache(entry);
    
    m_totalReceived++;
    m_lastReceiveTime = QDateTime::currentDateTime();
    
    // 发出数据准备就绪信号
    QVector<LogEntry> singleEntry;
    singleEntry.append(entry);
    emit dataReady(singleEntry);
    
    qDebug() << "New log entry received:" << entry.message();
}

void SimpleLog4QtDataSource::checkForNewLogs()
{
    // 这个方法在实际的Log4Qt集成中可能需要检查是否有新的日志
    // 在简化版本中，主要依赖于Appender的实时通知

    // 定期清理缓存 - 改为更频繁的检查
    static int checkCount = 0;
    checkCount++;

    if (checkCount % CACHE_CHECK_INTERVAL == 0) { // 每10秒清理一次
        cleanupCache();
    }

    // 内存使用监控
    if (checkCount % 30 == 0) { // 每30秒检查一次内存使用
        checkMemoryUsage();
    }
}

// ========== 私有方法实现 ==========

bool SimpleLog4QtDataSource::initializeLog4Qt()
{
#ifdef LOG4QT_AVAILABLE
    try {
        // 使用用户的Logger类进行初始化
        // 假设日志路径为当前目录下的logs文件夹
        QString logPath = QCoreApplication::applicationDirPath() + "/logs";
        Logger::init(logPath);
        qDebug() << "Log4Qt initialized with custom Logger configuration";
        return true;
    } catch (const std::exception& e) {
        qWarning() << "Log4Qt initialization failed:" << e.what();
        return false;
    }
#else
    qWarning() << "Log4Qt is not available in this build";
    return false;
#endif
}

bool SimpleLog4QtDataSource::createLogger()
{
#ifdef LOG4QT_AVAILABLE
    try {
        // 使用用户的Logger类获取根Logger
        Logger userLogger = Logger::getInstance();
        m_logger = userLogger.getRootLogger();

        if (m_logger) {
            qDebug() << "Logger obtained from user Logger class:" << m_loggerName;
            return true;
        } else {
            qWarning() << "Failed to get logger from user Logger class:" << m_loggerName;
            return false;
        }
    } catch (const std::exception& e) {
        qWarning() << "Exception creating logger:" << e.what();
        return false;
    }
#else
    // 在没有Log4Qt的情况下，创建一个模拟的logger指针
    m_logger = reinterpret_cast<void*>(0x1); // 非空指针表示"已创建"
    qDebug() << "Mock logger created for:" << m_loggerName;
    return true;
#endif
}

bool SimpleLog4QtDataSource::createAppender()
{
    try {
        // 创建简化的Appender
        m_appender = new SimpleLogViewerAppender(this);

        // 连接Appender信号
        connect(m_appender, &SimpleLogViewerAppender::newLogEntry,
                this, &SimpleLog4QtDataSource::onNewLogEntry);

#ifdef LOG4QT_AVAILABLE
        if (m_logger) {
            // 真正将Appender添加到Logger，建立真实的Log4Qt连接
            Log4Qt::Logger* log4qtLogger = static_cast<Log4Qt::Logger*>(m_logger);

            // 设置Appender名称
            m_appender->setName("SimpleLogViewerAppender");

            // 将Appender添加到Logger
            log4qtLogger->addAppender(m_appender);

            qDebug() << "SimpleLogViewerAppender successfully added to Log4Qt logger";
        }
#endif

        qDebug() << "SimpleLogViewerAppender created and configured";

        // 生成一些测试日志来验证连接
        generateTestData(5);

        return true;
    } catch (const std::exception& e) {
        qWarning() << "Exception creating appender:" << e.what();
        return false;
    }
}

void SimpleLog4QtDataSource::cleanupLog4Qt()
{
    // 断开Appender信号
    if (m_appender) {
        // 立即断开所有信号连接，防止在删除过程中收到信号
        QObject::disconnect(m_appender, nullptr, this, nullptr);

#ifdef LOG4QT_AVAILABLE
        if (m_logger) {
            // 从Logger中移除Appender
            Log4Qt::Logger* log4qtLogger = static_cast<Log4Qt::Logger*>(m_logger);
            log4qtLogger->removeAppender("SimpleLogViewerAppender");
            qDebug() << "SimpleLogViewerAppender removed from Log4Qt logger";
        }
#endif

        // 直接删除而不是使用deleteLater()，避免依赖事件循环
        // 这样可以确保在程序异常退出时也能正确清理内存
        delete m_appender;
        m_appender = nullptr;
        qDebug() << "Appender cleaned up immediately";
    }

    m_logger = nullptr;
    qDebug() << "Log4Qt resources cleaned up";
}

bool SimpleLog4QtDataSource::isLog4QtAvailable() const
{
#ifdef LOG4QT_AVAILABLE
    return true;
#else
    // 在没有Log4Qt的情况下，不允许连接
    return false;
#endif
}

void SimpleLog4QtDataSource::addToCache(const LogEntry& entry)
{
    QMutexLocker locker(&m_cacheMutex);

    // 添加新条目
    m_cachedEntries.append(entry);

    // 紧急清理机制 - 防止内存无限增长
    if (m_cachedEntries.size() > EMERGENCY_CLEANUP_THRESHOLD) {
        int removeCount = m_cachedEntries.size() - m_maxCacheEntries;
        m_cachedEntries.remove(0, removeCount);
        qWarning() << "Emergency cache cleanup triggered, removed" << removeCount << "entries";
        return; // 紧急清理后直接返回
    }

    // 检查是否超过最大缓存限制
    if (m_cachedEntries.size() > m_maxCacheEntries) {
        int removeCount = m_cachedEntries.size() - m_maxCacheEntries;
        m_cachedEntries.remove(0, removeCount);
        qDebug() << "Cache overflow, removed" << removeCount << "oldest entries";
    }
}

void SimpleLog4QtDataSource::cleanupCache()
{
    QMutexLocker locker(&m_cacheMutex);

    int currentSize = m_cachedEntries.size();

    // 如果缓存超过清理阈值，删除一些旧条目
    if (currentSize > CACHE_CLEANUP_THRESHOLD) {
        int removeCount = currentSize - m_maxCacheEntries;
        if (removeCount > 0) {
            m_cachedEntries.remove(0, removeCount);

            // 压缩内存以释放未使用的空间
            m_cachedEntries.squeeze();

            qDebug() << "Cache cleanup, removed" << removeCount << "entries, compressed memory";
        }
    }

    // 定期压缩内存（即使没有删除条目）
    static int compressionCounter = 0;
    compressionCounter++;
    if (compressionCounter % 100 == 0) { // 每100次清理进行一次内存压缩
        m_cachedEntries.squeeze();
        qDebug() << "Periodic memory compression performed";
    }
}

void SimpleLog4QtDataSource::checkMemoryUsage()
{
    QMutexLocker locker(&m_cacheMutex);

    int currentCacheSize = m_cachedEntries.size();

    // 计算内存使用百分比
    double usagePercentage = (double)currentCacheSize / m_maxCacheEntries * 100.0;
    double emergencyPercentage = (double)currentCacheSize / EMERGENCY_CLEANUP_THRESHOLD * 100.0;

    // 检查是否接近紧急清理阈值
    if (currentCacheSize > (EMERGENCY_CLEANUP_THRESHOLD * 0.8)) {
        qWarning() << "Memory usage warning: cache size" << currentCacheSize
                   << "(" << QString::number(emergencyPercentage, 'f', 1) << "% of emergency threshold)"
                   << "approaching emergency threshold" << EMERGENCY_CLEANUP_THRESHOLD;

        // 提前进行清理
        if (currentCacheSize > m_maxCacheEntries) {
            int removeCount = currentCacheSize - m_maxCacheEntries;
            m_cachedEntries.remove(0, removeCount);
            m_cachedEntries.squeeze(); // 压缩内存
            qDebug() << "Preventive cache cleanup, removed" << removeCount << "entries and compressed memory";
        }
    }

    // 记录详细的内存使用统计
    static int logCounter = 0;
    logCounter++;

    // 每10次检查记录一次详细信息
    if (logCounter % 10 == 0 || currentCacheSize > m_maxCacheEntries) {
        qDebug() << "Memory usage statistics:"
                 << "Cache size:" << currentCacheSize
                 << "Max entries:" << m_maxCacheEntries
                 << "Usage:" << QString::number(usagePercentage, 'f', 1) << "%"
                 << "Emergency threshold:" << EMERGENCY_CLEANUP_THRESHOLD
                 << "Total received:" << m_totalReceived;
    }

    // 检查是否需要发出内存警告信号
    if (usagePercentage > 90.0) {
        emitStatusChanged(QString("内存使用率较高: %1%").arg(QString::number(usagePercentage, 'f', 1)));
    }
}

void SimpleLog4QtDataSource::emitError(const QString& error)
{
    qWarning() << "SimpleLog4QtDataSource error:" << error;
    emit IDataSource::error(error);
}

void SimpleLog4QtDataSource::emitStatusChanged(const QString& status)
{
    qDebug() << "SimpleLog4QtDataSource status:" << status;
    emit statusChanged(status);
}
