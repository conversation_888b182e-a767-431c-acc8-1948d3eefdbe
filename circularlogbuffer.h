#ifndef CIRCULARLOGBUFFER_H
#define CIRCULARLOGBUFFER_H

#include "logentry.h"
#include "logviewer_global.h"
#include <QVector>
#include <QMutex>

/**
 * @brief 高效的环形日志缓冲区
 * 
 * 使用环形缓冲区替代QVector，避免频繁的remove(0, count)操作
 * 特点：
 * - O(1)的添加和删除操作
 * - 固定大小的内存使用
 * - 线程安全
 * - 自动覆盖旧数据
 */
class LOGVIEWER_EXPORT CircularLogBuffer
{
public:
    /**
     * @brief 构造函数
     * @param capacity 缓冲区容量
     */
    explicit CircularLogBuffer(int capacity = 50000);
    
    /**
     * @brief 析构函数
     */
    ~CircularLogBuffer();

    /**
     * @brief 添加日志条目（O(1)操作）
     * @param entry 日志条目
     */
    void append(const LogEntry& entry);
    
    /**
     * @brief 批量添加日志条目
     * @param entries 日志条目列表
     */
    void append(const QVector<LogEntry>& entries);
    
    /**
     * @brief 获取指定索引的日志条目
     * @param index 索引（相对于当前可见数据）
     * @return 日志条目，如果索引无效返回默认构造的LogEntry
     */
    LogEntry at(int index) const;
    
    /**
     * @brief 获取指定范围的日志条目
     * @param startIndex 起始索引
     * @param count 数量
     * @return 日志条目列表
     */
    QVector<LogEntry> mid(int startIndex, int count) const;
    
    /**
     * @brief 获取所有日志条目
     * @return 所有日志条目的副本
     */
    QVector<LogEntry> toVector() const;
    
    /**
     * @brief 获取当前大小
     * @return 当前存储的日志条目数量
     */
    int size() const;
    
    /**
     * @brief 获取容量
     * @return 缓冲区容量
     */
    int capacity() const;
    
    /**
     * @brief 检查是否为空
     * @return 是否为空
     */
    bool isEmpty() const;
    
    /**
     * @brief 检查是否已满
     * @return 是否已满
     */
    bool isFull() const;
    
    /**
     * @brief 清空缓冲区
     */
    void clear();
    
    /**
     * @brief 设置新的容量
     * @param newCapacity 新容量
     */
    void setCapacity(int newCapacity);
    
    /**
     * @brief 获取内存使用统计
     * @return 内存使用字节数（估算）
     */
    qint64 memoryUsage() const;
    
    /**
     * @brief 压缩内存（释放未使用的空间）
     */
    void squeeze();

private:
    QVector<LogEntry> m_buffer;     ///< 内部缓冲区
    int m_capacity;                 ///< 缓冲区容量
    int m_head;                     ///< 头指针（下一个写入位置）
    int m_size;                     ///< 当前大小
    mutable QMutex m_mutex;         ///< 线程安全锁
    
    /**
     * @brief 计算实际索引
     * @param logicalIndex 逻辑索引
     * @return 实际索引
     */
    int realIndex(int logicalIndex) const;
    
    /**
     * @brief 扩展缓冲区容量
     * @param newCapacity 新容量
     */
    void expandCapacity(int newCapacity);
};

#endif // CIRCULARLOGBUFFER_H
