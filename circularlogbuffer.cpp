#include "circularlogbuffer.h"
#include <QMutexLocker>
#include <QDebug>
#include <algorithm>

CircularLogBuffer::CircularLogBuffer(int capacity)
    : m_capacity(qMax(1, capacity))
    , m_head(0)
    , m_size(0)
{
    m_buffer.resize(m_capacity);
    qDebug() << "CircularLogBuffer created with capacity:" << m_capacity;
}

CircularLogBuffer::~CircularLogBuffer()
{
    qDebug() << "CircularLogBuffer destroyed";
}

void CircularLogBuffer::append(const LogEntry& entry)
{
    QMutexLocker locker(&m_mutex);
    
    // 在头指针位置写入新条目
    m_buffer[m_head] = entry;
    
    // 移动头指针
    m_head = (m_head + 1) % m_capacity;
    
    // 更新大小（最大不超过容量）
    if (m_size < m_capacity) {
        m_size++;
    }
}

void CircularLogBuffer::append(const QVector<LogEntry>& entries)
{
    QMutexLocker locker(&m_mutex);
    
    for (const LogEntry& entry : entries) {
        // 在头指针位置写入新条目
        m_buffer[m_head] = entry;
        
        // 移动头指针
        m_head = (m_head + 1) % m_capacity;
        
        // 更新大小（最大不超过容量）
        if (m_size < m_capacity) {
            m_size++;
        }
    }
}

LogEntry CircularLogBuffer::at(int index) const
{
    QMutexLocker locker(&m_mutex);
    
    if (index < 0 || index >= m_size) {
        return LogEntry(); // 返回默认构造的LogEntry
    }
    
    int actualIndex = realIndex(index);
    return m_buffer[actualIndex];
}

QVector<LogEntry> CircularLogBuffer::mid(int startIndex, int count) const
{
    QMutexLocker locker(&m_mutex);
    
    QVector<LogEntry> result;
    
    if (startIndex < 0 || startIndex >= m_size || count <= 0) {
        return result;
    }
    
    int actualCount = qMin(count, m_size - startIndex);
    result.reserve(actualCount);
    
    for (int i = 0; i < actualCount; ++i) {
        int actualIndex = realIndex(startIndex + i);
        result.append(m_buffer[actualIndex]);
    }
    
    return result;
}

QVector<LogEntry> CircularLogBuffer::toVector() const
{
    QMutexLocker locker(&m_mutex);
    
    QVector<LogEntry> result;
    result.reserve(m_size);
    
    for (int i = 0; i < m_size; ++i) {
        int actualIndex = realIndex(i);
        result.append(m_buffer[actualIndex]);
    }
    
    return result;
}

int CircularLogBuffer::size() const
{
    QMutexLocker locker(&m_mutex);
    return m_size;
}

int CircularLogBuffer::capacity() const
{
    QMutexLocker locker(&m_mutex);
    return m_capacity;
}

bool CircularLogBuffer::isEmpty() const
{
    QMutexLocker locker(&m_mutex);
    return m_size == 0;
}

bool CircularLogBuffer::isFull() const
{
    QMutexLocker locker(&m_mutex);
    return m_size == m_capacity;
}

void CircularLogBuffer::clear()
{
    QMutexLocker locker(&m_mutex);
    m_head = 0;
    m_size = 0;
    // 不需要清除实际数据，只需重置指针
}

void CircularLogBuffer::setCapacity(int newCapacity)
{
    QMutexLocker locker(&m_mutex);
    
    if (newCapacity <= 0 || newCapacity == m_capacity) {
        return;
    }
    
    if (newCapacity > m_capacity) {
        // 扩展容量
        expandCapacity(newCapacity);
    } else {
        // 缩小容量，保留最新的数据
        QVector<LogEntry> currentData = toVector();
        
        m_capacity = newCapacity;
        m_buffer.resize(m_capacity);
        m_head = 0;
        m_size = 0;
        
        // 重新添加数据，保留最新的条目
        int startIndex = qMax(0, currentData.size() - newCapacity);
        for (int i = startIndex; i < currentData.size(); ++i) {
            m_buffer[m_head] = currentData[i];
            m_head = (m_head + 1) % m_capacity;
            m_size++;
        }
    }
    
    qDebug() << "CircularLogBuffer capacity changed to:" << m_capacity;
}

qint64 CircularLogBuffer::memoryUsage() const
{
    QMutexLocker locker(&m_mutex);
    
    // 估算内存使用：缓冲区大小 + 每个LogEntry的大小
    // 这是一个粗略的估算
    qint64 bufferSize = m_capacity * sizeof(LogEntry);
    qint64 stringMemory = 0;
    
    // 估算字符串内存使用（采样前100个条目）
    int sampleSize = qMin(100, m_size);
    for (int i = 0; i < sampleSize; ++i) {
        int actualIndex = realIndex(i);
        const LogEntry& entry = m_buffer[actualIndex];
        stringMemory += entry.source().size() * sizeof(QChar);
        stringMemory += entry.message().size() * sizeof(QChar);
        stringMemory += entry.details().size() * sizeof(QChar);
    }
    
    // 根据采样估算总内存
    if (sampleSize > 0) {
        stringMemory = (stringMemory * m_size) / sampleSize;
    }
    
    return bufferSize + stringMemory;
}

void CircularLogBuffer::squeeze()
{
    QMutexLocker locker(&m_mutex);
    
    // 对于环形缓冲区，squeeze操作主要是确保缓冲区大小合适
    if (m_size < m_capacity / 2 && m_capacity > 1000) {
        // 如果使用率低于50%且容量较大，考虑缩小
        int newCapacity = qMax(1000, m_size * 2);
        setCapacity(newCapacity);
    }
}

int CircularLogBuffer::realIndex(int logicalIndex) const
{
    // 计算实际的缓冲区索引
    // 如果缓冲区未满，从0开始
    // 如果缓冲区已满，从最旧的数据开始
    if (m_size < m_capacity) {
        return logicalIndex;
    } else {
        return (m_head + logicalIndex) % m_capacity;
    }
}

void CircularLogBuffer::expandCapacity(int newCapacity)
{
    // 扩展容量时需要重新排列数据
    QVector<LogEntry> newBuffer(newCapacity);
    
    // 复制现有数据到新缓冲区
    for (int i = 0; i < m_size; ++i) {
        int actualIndex = realIndex(i);
        newBuffer[i] = m_buffer[actualIndex];
    }
    
    m_buffer = newBuffer;
    m_capacity = newCapacity;
    m_head = m_size; // 头指针指向下一个可写位置
}
