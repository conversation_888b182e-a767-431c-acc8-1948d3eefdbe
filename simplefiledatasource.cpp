#include "simplefiledatasource.h"
#include "simpleconfigmanager.h"
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QRegularExpression>
#include <QDateTime>
#include <QDebug>
#include <QApplication>

// ========== 常量定义 ==========
const qint64 SimpleFileDataSource::MAX_FILE_SIZE_BYTES = 500 * 1024 * 1024; // 500MB
const int SimpleFileDataSource::MAX_LINES_TO_PROCESS = 100000; // 最大处理10万行
const int SimpleFileDataSource::ENCODING_DETECTION_BYTES = 8192; // 编码检测用8KB

SimpleFileDataSource::SimpleFileDataSource(QObject* parent)
    : IDataSource(parent)
    , m_filePath("")
    , m_encoding("UTF-8")
    , m_connected(false)
    , m_fileWatchingEnabled(false)
    , m_fileWatcher(nullptr)
    , m_lastFileSize(0)
{
    // 从配置管理器获取默认编码
    SimpleConfigManager& config = SimpleConfigManager::instance();
    m_encoding = config.getDefaultEncoding();
    
    // 创建文件监控器
    m_fileWatcher = new QFileSystemWatcher(this);
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged,
            this, &SimpleFileDataSource::onFileChanged);
    
    qDebug() << "SimpleFileDataSource created with default encoding:" << m_encoding;
}

SimpleFileDataSource::~SimpleFileDataSource()
{
    disconnect();
    qDebug() << "SimpleFileDataSource destroyed";
}

bool SimpleFileDataSource::connectToSource()
{
    if (m_filePath.isEmpty()) {
        emitError("文件路径为空");
        return false;
    }
    
    if (!checkFileAccess()) {
        emitError(QString("无法访问文件: %1").arg(m_filePath));
        return false;
    }
    
    if (!checkFileSize()) {
        emitError(QString("文件过大，超过限制: %1 MB").arg(MAX_FILE_SIZE_BYTES / 1024 / 1024));
        return false;
    }
    
    // 智能检测编码
    QTextCodec* detectedCodec = detectFileEncoding(m_filePath);
    if (detectedCodec) {
        QString detectedEncoding = detectedCodec->name();
        if (detectedEncoding != m_encoding) {
            qDebug() << QString("检测到编码: %1，当前设置: %2").arg(detectedEncoding, m_encoding);
            // 可以选择使用检测到的编码或保持用户设置
        }
    }
    
    m_connected = true;
    
    // 启用文件监控（如果需要）
    if (m_fileWatchingEnabled && !m_fileWatcher->files().contains(m_filePath)) {
        m_fileWatcher->addPath(m_filePath);
        qDebug() << "File watching enabled for:" << m_filePath;
    }
    
    // 更新文件信息
    QFileInfo fileInfo(m_filePath);
    m_lastFileSize = fileInfo.size();
    m_lastModified = fileInfo.lastModified();
    
    emitStatusChanged(QString("已连接到文件: %1").arg(QFileInfo(m_filePath).fileName()));
    
    qDebug() << "Connected to file:" << m_filePath;
    return true;
}

QVector<LogEntry> SimpleFileDataSource::loadData()
{
    QVector<LogEntry> entries;
    
    if (!m_connected) {
        emitError("未连接到数据源");
        return entries;
    }
    
    QFile file(m_filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emitError(QString("无法打开文件: %1").arg(file.errorString()));
        return entries;
    }
    
    // 设置编码
    QTextCodec* codec = QTextCodec::codecForName(m_encoding.toUtf8());
    if (!codec) {
        qWarning() << "Unknown encoding:" << m_encoding << ", using UTF-8";
        codec = QTextCodec::codecForName("UTF-8");
    }
    
    QTextStream stream(&file);
    stream.setCodec(codec);
    
    emitStatusChanged("正在读取文件...");
    
    int lineNumber = 0;
    int processedLines = 0;
    QString line;
    
    // 获取配置的最大条目数
    SimpleConfigManager& config = SimpleConfigManager::instance();
    int maxEntries = config.getMaxLogEntries();
    
    while (!stream.atEnd() && processedLines < MAX_LINES_TO_PROCESS) {
        line = stream.readLine();
        lineNumber++;
        
        if (line.trimmed().isEmpty()) {
            continue; // 跳过空行
        }
        
        // 解析日志行
        LogEntry entry = parseLogLine(line, lineNumber);
        entries.append(entry);
        
        processedLines++;
        
        // 检查是否超过最大条目数
        if (entries.size() >= maxEntries) {
            qDebug() << QString("达到最大条目数限制: %1").arg(maxEntries);
            break;
        }
        
        // 定期更新状态
        if (processedLines % 1000 == 0) {
            emitStatusChanged(QString("已读取 %1 行...").arg(processedLines));
            QApplication::processEvents(); // 保持UI响应
        }
    }
    
    file.close();
    
    emitStatusChanged(QString("文件读取完成，共 %1 条日志").arg(entries.size()));
    
    qDebug() << QString("Loaded %1 entries from file, processed %2 lines")
                .arg(entries.size()).arg(processedLines);
    
    return entries;
}

void SimpleFileDataSource::disconnect()
{
    if (m_fileWatcher && !m_fileWatcher->files().isEmpty()) {
        m_fileWatcher->removePaths(m_fileWatcher->files());
    }
    
    m_connected = false;
    emitStatusChanged("已断开连接");
    
    qDebug() << "Disconnected from file data source";
}

bool SimpleFileDataSource::isConnected() const
{
    return m_connected;
}

QString SimpleFileDataSource::getSourceInfo() const
{
    if (m_filePath.isEmpty()) {
        return "未设置文件路径";
    }
    
    QFileInfo fileInfo(m_filePath);
    QString info = QString("文件: %1\n").arg(fileInfo.fileName());
    info += QString("路径: %1\n").arg(fileInfo.absoluteFilePath());
    info += QString("大小: %1 字节\n").arg(fileInfo.size());
    info += QString("编码: %1\n").arg(m_encoding);
    info += QString("修改时间: %1").arg(fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss"));
    
    return info;
}

void SimpleFileDataSource::setFilePath(const QString& filePath)
{
    if (m_filePath != filePath) {
        // 如果已连接，先断开
        if (m_connected) {
            disconnect();
        }
        
        m_filePath = filePath;
        qDebug() << "File path set to:" << filePath;
    }
}

void SimpleFileDataSource::setEncoding(const QString& encoding)
{
    if (m_encoding != encoding) {
        m_encoding = encoding;
        qDebug() << "Encoding set to:" << encoding;
    }
}

void SimpleFileDataSource::setFileWatchingEnabled(bool enabled)
{
    m_fileWatchingEnabled = enabled;
    
    if (m_connected && !m_filePath.isEmpty()) {
        if (enabled && !m_fileWatcher->files().contains(m_filePath)) {
            m_fileWatcher->addPath(m_filePath);
            qDebug() << "File watching enabled";
        } else if (!enabled && m_fileWatcher->files().contains(m_filePath)) {
            m_fileWatcher->removePath(m_filePath);
            qDebug() << "File watching disabled";
        }
    }
}

qint64 SimpleFileDataSource::getFileSize() const
{
    if (m_filePath.isEmpty()) {
        return -1;
    }
    
    QFileInfo fileInfo(m_filePath);
    return fileInfo.exists() ? fileInfo.size() : -1;
}

int SimpleFileDataSource::getEstimatedLineCount() const
{
    qint64 fileSize = getFileSize();
    if (fileSize <= 0) {
        return 0;
    }
    
    // 估算：平均每行100字节
    return static_cast<int>(fileSize / 100);
}

void SimpleFileDataSource::onFileChanged(const QString& path)
{
    if (path == m_filePath && m_connected) {
        QFileInfo fileInfo(m_filePath);
        qint64 currentSize = fileInfo.size();

        if (currentSize != m_lastFileSize) {
            qDebug() << QString("File changed: %1, size: %2 -> %3")
                        .arg(path).arg(m_lastFileSize).arg(currentSize);

            m_lastFileSize = currentSize;
            m_lastModified = fileInfo.lastModified();

            emitStatusChanged("文件已更新");
            // 这里可以选择自动重新加载或仅通知
        }
    }
}

// ========== 私有方法实现 ==========

bool SimpleFileDataSource::checkFileAccess() const
{
    QFileInfo fileInfo(m_filePath);

    if (!fileInfo.exists()) {
        qDebug() << "File does not exist:" << m_filePath;
        return false;
    }

    if (!fileInfo.isFile()) {
        qDebug() << "Path is not a file:" << m_filePath;
        return false;
    }

    if (!fileInfo.isReadable()) {
        qDebug() << "File is not readable:" << m_filePath;
        return false;
    }

    return true;
}

bool SimpleFileDataSource::checkFileSize() const
{
    QFileInfo fileInfo(m_filePath);
    qint64 fileSize = fileInfo.size();

    // 从配置获取最大文件大小限制
    SimpleConfigManager& config = SimpleConfigManager::instance();
    qint64 maxSizeBytes = static_cast<qint64>(config.getMaxFileSize()) * 1024 * 1024;

    if (fileSize > maxSizeBytes) {
        qDebug() << QString("File too large: %1 bytes, limit: %2 bytes")
                    .arg(fileSize).arg(maxSizeBytes);
        return false;
    }

    return true;
}

QTextCodec* SimpleFileDataSource::detectFileEncoding(const QString& filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return nullptr;
    }

    // 读取文件开头用于编码检测
    QByteArray data = file.read(ENCODING_DETECTION_BYTES);
    file.close();

    if (data.isEmpty()) {
        return nullptr;
    }

    // 检查BOM
    if (data.startsWith("\xEF\xBB\xBF")) {
        qDebug() << "Detected UTF-8 BOM";
        return QTextCodec::codecForName("UTF-8");
    }

    if (data.startsWith("\xFF\xFE") || data.startsWith("\xFE\xFF")) {
        qDebug() << "Detected UTF-16 BOM";
        return QTextCodec::codecForName("UTF-16");
    }

    // 尝试UTF-8解码
    QTextCodec* utf8Codec = QTextCodec::codecForName("UTF-8");
    QTextCodec::ConverterState state;
    QString utf8Text = utf8Codec->toUnicode(data.constData(), data.size(), &state);

    if (state.invalidChars == 0) {
        // UTF-8解码成功，检查是否包含非ASCII字符（修复后的逻辑）
        bool hasNonAscii = false;
        bool hasChinese = false;

        for (const QChar& ch : utf8Text) {
            // 检查中文字符范围
            if (ch.unicode() >= 0x4E00 && ch.unicode() <= 0x9FFF) {
                hasChinese = true;
                hasNonAscii = true;
                break;
            }
            // 检查是否有非ASCII字符（大于127的字符）
            if (ch.unicode() > 127) {
                hasNonAscii = true;
            }
        }

        // 如果包含中文字符或其他非ASCII字符，认为是UTF-8编码
        if (hasChinese || hasNonAscii) {
            qDebug() << "Detected UTF-8 encoding with non-ASCII characters";
            return utf8Codec;
        }

        // 如果只包含ASCII字符，也可能是UTF-8（ASCII是UTF-8的子集）
        qDebug() << "Detected UTF-8 encoding (ASCII compatible)";
        return utf8Codec;
    }

    // 尝试GBK解码
    QTextCodec* gbkCodec = QTextCodec::codecForName("GBK");
    if (gbkCodec) {
        QTextCodec::ConverterState gbkState;
        QString gbkText = gbkCodec->toUnicode(data.constData(), data.size(), &gbkState);

        if (gbkState.invalidChars == 0) {
            // 检查是否包含中文字符
            for (const QChar& ch : gbkText) {
                if (ch.unicode() >= 0x4E00 && ch.unicode() <= 0x9FFF) {
                    qDebug() << "Detected GBK encoding";
                    return gbkCodec;
                }
            }
        }
    }

    // 默认返回UTF-8
    qDebug() << "Using default UTF-8 encoding";
    return utf8Codec;
}

LogEntry SimpleFileDataSource::parseLogLine(const QString& line, int lineNumber) const
{
    // 尝试解析时间戳
    QDateTime timestamp = parseTimestamp(line);

    // 检测日志级别
    LogEntry::LogLevel level = detectLogLevel(line);

    // 构建源信息
    QString source = QString("File:%1").arg(lineNumber);

    // 消息内容（去除时间戳和级别信息后的内容）
    QString message = line.trimmed();

    return LogEntry(timestamp, level, source, message, "");
}

LogEntry::LogLevel SimpleFileDataSource::detectLogLevel(const QString& text) const
{
    QString upperText = text.toUpper();

    if (upperText.contains("CRITICAL") || upperText.contains("FATAL")) {
        return LogEntry::LogLevel::Critical;
    } else if (upperText.contains("ERROR")) {
        return LogEntry::LogLevel::Error;
    } else if (upperText.contains("WARN")) {
        return LogEntry::LogLevel::Warning;
    } else if (upperText.contains("DEBUG")) {
        return LogEntry::LogLevel::Debug;
    } else {
        return LogEntry::LogLevel::Info; // 默认级别
    }
}

QDateTime SimpleFileDataSource::parseTimestamp(const QString& text) const
{
    // 常见的时间戳格式
    QStringList patterns = {
        "yyyy-MM-dd hh:mm:ss.zzz",
        "yyyy-MM-dd hh:mm:ss",
        "yyyy/MM/dd hh:mm:ss",
        "MM-dd hh:mm:ss",
        "hh:mm:ss.zzz",
        "hh:mm:ss"
    };

    for (const QString& pattern : patterns) {
        QRegularExpression regex("\\b\\d");
        QRegularExpressionMatch match = regex.match(text);

        if (match.hasMatch()) {
            // 提取可能的时间戳字符串
            QRegularExpression timeRegex("\\d{1,4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{1,2}:\\d{1,2}(?:\\.\\d{1,3})?|\\d{1,2}:\\d{1,2}:\\d{1,2}(?:\\.\\d{1,3})?");
            QRegularExpressionMatch timeMatch = timeRegex.match(text);

            if (timeMatch.hasMatch()) {
                QString timeStr = timeMatch.captured(0);
                QDateTime dateTime = QDateTime::fromString(timeStr, pattern);

                if (dateTime.isValid()) {
                    // 如果只有时间没有日期，使用当前日期
                    if (!timeStr.contains("-") && !timeStr.contains("/")) {
                        QDate currentDate = QDate::currentDate();
                        dateTime.setDate(currentDate);
                    }
                    return dateTime;
                }
            }
        }
    }

    // 如果无法解析时间戳，返回当前时间
    return QDateTime::currentDateTime();
}

void SimpleFileDataSource::emitError(const QString& error)
{
    qWarning() << "SimpleFileDataSource error:" << error;
    emit IDataSource::error(error);
}

void SimpleFileDataSource::emitStatusChanged(const QString& status)
{
    qDebug() << "SimpleFileDataSource status:" << status;
    emit statusChanged(status);
}
